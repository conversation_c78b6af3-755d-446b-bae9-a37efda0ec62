# Guida Tecnica - Simulatore CNC

## Architettura del Sistema

### Componenti Principali

#### 1. **MillingTool** - Gestione Utensile
```python
class MillingTool:
    - diameter: float          # Diametro fresa (mm)
    - radius: float           # Raggio calcolato
    - length: float           # Lunghezza utensile
    - tool_type: str          # Tipo (end_mill, ball_mill, etc.)
```

#### 2. **CNCSimulator** - Motore Principale
```python
class CNCSimulator:
    - model_shape             # Geometria 3D del pezzo
    - bounding_box           # Volume di lavorazione
    - milling_tool           # Utensile corrente
    - machining_axis         # Asse di lavorazione (X/Y/Z)
    - machining_direction    # Direzione (+/-)
    - toolpath_points        # Punti del percorso
    - toolpath_lines         # Segmenti di collegamento
```

### Algoritmi Implementati

#### Generazione Percorso Zig-Zag
```
Per ogni livello Z:
    y = y_min
    direzione_x = 1
    
    Mentre y <= y_max:
        Se direzione_x > 0:
            x: x_min → x_max (step = stepover)
        Altrimenti:
            x: x_max → x_min (step = stepover)
        
        Per ogni x:
            punto = (x, y, z)
            Se NON collisione(punto):
                aggiungi_punto(punto)
        
        y += stepover
        direzione_x *= -1  # Inverte direzione
```

#### Rilevamento Collisioni
```python
def check_collision(tool_position, tool_direction):
    tool_geometry = create_cylinder(position, direction, radius, length)
    distance = calculate_min_distance(tool_geometry, workpiece)
    return distance < tolerance  # 0.1mm default
```

## Parametri di Lavorazione

### Stepover (Passo Laterale)
- **Definizione**: Distanza tra passate parallele
- **Range tipico**: 30-80% del diametro fresa
- **Effetto**: 
  - Basso → Maggiore precisione, più tempo
  - Alto → Minore precisione, meno tempo

### Stepdown (Profondità di Passata)
- **Definizione**: Profondità di taglio per passata
- **Range tipico**: 10-50% del diametro fresa
- **Effetto**:
  - Basso → Taglio più dolce, più passate
  - Alto → Taglio aggressivo, meno passate

### Offset di Sicurezza
- **Definizione**: Margine attorno al bounding box
- **Default**: 5mm
- **Scopo**: Evitare collisioni con morsetti/attrezzature

## Formati di Output

### G-code Standard
```gcode
; Header con metadati
G21                    ; Unità millimetriche
G90                    ; Coordinate assolute
G17                    ; Piano di lavoro XY
M3 S12000             ; Avvio mandrino 12000 RPM

; Posizionamento iniziale
G0 X-5.000 Y-5.000 Z105.000

; Lavorazione
G1 X-1.000 Y-5.000 Z105.000 F800
G1 X3.000 Y-5.000 Z105.000 F800
...

; Chiusura
M5                    ; Spegni mandrino
G0 Z50               ; Allontanamento sicurezza
M30                  ; Fine programma
```

### Metadati nei Commenti
```gcode
; Fresa: D6.0mm
; Asse lavorazione: Z-
; Punti totali: 43904
```

## Ottimizzazioni Performance

### Riduzione Punti
1. **Aumenta Stepover**: Meno passate parallele
2. **Aumenta Stepdown**: Meno livelli Z
3. **Fresa più grande**: Copertura maggiore per passata

### Riduzione Tempo Calcolo
1. **Disabilita controllo collisioni**: Per test rapidi
2. **Bounding box più piccolo**: Riduci area di lavoro
3. **Parametri più grossolani**: Meno iterazioni

### Ottimizzazione Percorso
- **Pattern Zig-Zag**: Riduce spostamenti a vuoto
- **Ordinamento punti**: Minimizza distanze
- **Eliminazione ridondanze**: Rimuove punti duplicati

## Analisi Risultati

### Metriche Chiave
- **Punti totali**: Indicatore di precisione
- **Distanza percorsa**: Correlata al tempo di lavorazione
- **Tempo stimato**: Basato su velocità 800 mm/min
- **Dimensioni bounding box**: Volume effettivo lavorato

### Confronto Strategie

| Strategia | Punti | Tempo | Precisione | Uso Consigliato |
|-----------|-------|-------|------------|------------------|
| Veloce    | 3K    | 25min | Bassa      | Sgrossatura      |
| Bilanciato| 13K   | 100min| Media      | Lavorazione std  |
| Preciso   | 104K  | 780min| Alta       | Semi-finitura    |
| Ultra     | 755K  | 5667min| Massima   | Finitura         |

## Estensioni Possibili

### Nuovi Algoritmi Percorso
- **Spirale**: Per cavità circolari
- **Offset**: Seguendo contorni
- **Adattivo**: Densità variabile
- **Trocoidale**: Per materiali duri

### Tipi di Fresa
- **Ball Mill**: Fresa a sfera per superfici curve
- **Tapered**: Fresa conica per angoli
- **T-Slot**: Per scanalature a T
- **Drill**: Per forature

### Controllo Avanzato
- **Velocità variabile**: F diversa per zone
- **Compensazione usura**: Correzione raggio
- **Controllo temperatura**: Pausa raffreddamento
- **Monitoraggio forze**: Adattamento parametri

## Debugging e Troubleshooting

### Problemi Comuni

#### Troppi Punti Generati
```python
# Soluzione: Aumenta parametri
stepover = tool_diameter * 0.8  # Invece di 0.6
stepdown = tool_diameter * 0.5  # Invece di 0.3
```

#### Collisioni False
```python
# Soluzione: Aumenta tolleranza
tolerance = 0.5  # Invece di 0.1mm
```

#### File G-code Troppo Grande
```python
# Soluzione: Decimazione punti
if len(points) > 50000:
    points = points[::2]  # Prendi 1 punto ogni 2
```

### Validazione Output
1. **Controllo sintassi G-code**: Parser dedicato
2. **Simulazione 3D**: Visualizzazione percorso
3. **Calcolo tempi**: Stima realistica
4. **Verifica limiti macchina**: Coordinate valide

## Integrazione con CAM

### Import da CAD
- **STEP/IGES**: Geometrie precise
- **STL**: Mesh triangolari
- **OBJ**: Modelli semplificati

### Export verso CNC
- **G-code ISO**: Standard universale
- **Heidenhain**: Controlli specifici
- **Fanuc**: Formato proprietario
- **LinuxCNC**: Open source

### Post-Processing
- **Correzione coordinate**: Offset origine
- **Scaling**: Adattamento unità
- **Rotazione**: Orientamento pezzo
- **Mirroring**: Simmetrie

## Best Practices

### Progettazione Percorso
1. **Inizia con sgrossatura**: Fresa grande, parametri aggressivi
2. **Semi-finitura**: Fresa media, parametri bilanciati  
3. **Finitura**: Fresa piccola, parametri conservativi
4. **Verifica sempre**: Simulazione prima della lavorazione

### Sicurezza
- **Offset adeguato**: Mai troppo vicino ai bordi
- **Velocità conservative**: Meglio lento che rotto
- **Controllo collisioni**: Sempre attivo in produzione
- **Backup frequenti**: Salva configurazioni funzionanti

### Performance
- **Profila il codice**: Identifica bottleneck
- **Cache risultati**: Evita ricalcoli
- **Parallelizzazione**: Multi-threading dove possibile
- **Memoria efficiente**: Gestione array grandi
