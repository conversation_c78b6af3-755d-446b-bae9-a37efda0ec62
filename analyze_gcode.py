#!/usr/bin/env python3
"""
Analizzatore di file G-code generati dal simulatore CNC
Fornisce statistiche dettagliate sui percorsi di lavorazione
"""

import os
import re
import glob
from typing import Dict, List, Tuple

class GCodeAnalyzer:
    """Analizzatore per file G-code"""
    
    def __init__(self):
        self.files_data = {}
    
    def analyze_file(self, filepath: str) -> Dict:
        """Analizza un singolo file G-code"""
        if not os.path.exists(filepath):
            return {}
        
        data = {
            'filename': os.path.basename(filepath),
            'filesize': os.path.getsize(filepath),
            'total_lines': 0,
            'gcode_lines': 0,
            'comments': 0,
            'movements': {'G0': 0, 'G1': 0},
            'coordinates': {'X': [], 'Y': [], 'Z': []},
            'tool_info': {},
            'estimated_time': 0,
            'total_distance': 0,
            'bounding_box': {'X': [float('inf'), float('-inf')], 
                           'Y': [float('inf'), float('-inf')], 
                           'Z': [float('inf'), float('-inf')]}
        }
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                data['total_lines'] = len(lines)
                
                prev_pos = {'X': 0, 'Y': 0, 'Z': 0}
                
                for line in lines:
                    line = line.strip()
                    
                    if not line:
                        continue
                    
                    if line.startswith(';'):
                        data['comments'] += 1
                        # Estrai informazioni dal commento
                        if 'Fresa:' in line:
                            match = re.search(r'Ø(\d+\.?\d*)mm', line)
                            if match:
                                data['tool_info']['diameter'] = float(match.group(1))
                        elif 'Asse lavorazione:' in line:
                            match = re.search(r'Asse lavorazione: ([XYZ][+-])', line)
                            if match:
                                data['tool_info']['axis'] = match.group(1)
                        elif 'Punti totali:' in line:
                            match = re.search(r'Punti totali: (\d+)', line)
                            if match:
                                data['tool_info']['total_points'] = int(match.group(1))
                        continue
                    
                    data['gcode_lines'] += 1
                    
                    # Analizza comandi G
                    if line.startswith('G0'):
                        data['movements']['G0'] += 1
                    elif line.startswith('G1'):
                        data['movements']['G1'] += 1
                    
                    # Estrai coordinate
                    current_pos = prev_pos.copy()
                    for axis in ['X', 'Y', 'Z']:
                        match = re.search(f'{axis}([+-]?\\d+\\.?\\d*)', line)
                        if match:
                            coord = float(match.group(1))
                            current_pos[axis] = coord
                            data['coordinates'][axis].append(coord)
                            
                            # Aggiorna bounding box
                            if coord < data['bounding_box'][axis][0]:
                                data['bounding_box'][axis][0] = coord
                            if coord > data['bounding_box'][axis][1]:
                                data['bounding_box'][axis][1] = coord
                    
                    # Calcola distanza se è un movimento
                    if line.startswith('G1'):
                        distance = ((current_pos['X'] - prev_pos['X'])**2 + 
                                  (current_pos['Y'] - prev_pos['Y'])**2 + 
                                  (current_pos['Z'] - prev_pos['Z'])**2)**0.5
                        data['total_distance'] += distance
                    
                    prev_pos = current_pos
                
                # Calcola tempo stimato (assumendo F800 = 800 mm/min)
                data['estimated_time'] = data['total_distance'] / 800  # minuti
                
        except Exception as e:
            print(f"Errore nell'analisi di {filepath}: {e}")
        
        return data
    
    def analyze_all_files(self, pattern: str = "*.nc") -> None:
        """Analizza tutti i file G-code nella directory"""
        files = glob.glob(pattern)
        
        for filepath in files:
            self.files_data[filepath] = self.analyze_file(filepath)
    
    def print_file_summary(self, filepath: str) -> None:
        """Stampa riassunto di un file"""
        if filepath not in self.files_data:
            print(f"File {filepath} non analizzato")
            return
        
        data = self.files_data[filepath]
        
        print(f"\n📄 {data['filename']}")
        print("=" * 50)
        print(f"Dimensione file: {data['filesize']:,} bytes")
        print(f"Righe totali: {data['total_lines']:,}")
        print(f"Righe G-code: {data['gcode_lines']:,}")
        print(f"Commenti: {data['comments']}")
        
        if data['tool_info']:
            print(f"\n🔧 Informazioni Utensile:")
            if 'diameter' in data['tool_info']:
                print(f"  Diametro fresa: Ø{data['tool_info']['diameter']}mm")
            if 'axis' in data['tool_info']:
                print(f"  Asse lavorazione: {data['tool_info']['axis']}")
            if 'total_points' in data['tool_info']:
                print(f"  Punti totali: {data['tool_info']['total_points']:,}")
        
        print(f"\n📊 Movimenti:")
        print(f"  Rapidi (G0): {data['movements']['G0']:,}")
        print(f"  Lavorazione (G1): {data['movements']['G1']:,}")
        
        print(f"\n📏 Dimensioni:")
        for axis in ['X', 'Y', 'Z']:
            if data['bounding_box'][axis][0] != float('inf'):
                min_val = data['bounding_box'][axis][0]
                max_val = data['bounding_box'][axis][1]
                size = max_val - min_val
                print(f"  {axis}: {min_val:.1f} → {max_val:.1f} mm (Δ{size:.1f}mm)")
        
        print(f"\n⏱️ Tempi:")
        print(f"  Distanza totale: {data['total_distance']:.1f} mm")
        print(f"  Tempo stimato: {data['estimated_time']:.1f} min ({data['estimated_time']/60:.1f} ore)")
    
    def print_comparison_table(self) -> None:
        """Stampa tabella comparativa di tutti i file"""
        if not self.files_data:
            print("Nessun file analizzato")
            return
        
        print("\n📊 CONFRONTO FILE G-CODE")
        print("=" * 100)
        
        # Header
        print(f"{'File':<25} {'Punti':<8} {'Fresa':<8} {'Asse':<6} {'Tempo':<8} {'Distanza':<10} {'Dimensioni':<15}")
        print("-" * 100)
        
        # Ordina per numero di punti
        sorted_files = sorted(self.files_data.items(), 
                            key=lambda x: x[1].get('tool_info', {}).get('total_points', 0))
        
        for filepath, data in sorted_files:
            filename = data['filename'][:24]
            points = data.get('tool_info', {}).get('total_points', 0)
            diameter = data.get('tool_info', {}).get('diameter', 0)
            axis = data.get('tool_info', {}).get('axis', 'N/A')
            time_min = data['estimated_time']
            distance = data['total_distance']
            
            # Calcola dimensioni bounding box
            sizes = []
            for ax in ['X', 'Y', 'Z']:
                if data['bounding_box'][ax][0] != float('inf'):
                    size = data['bounding_box'][ax][1] - data['bounding_box'][ax][0]
                    sizes.append(f"{size:.0f}")
                else:
                    sizes.append("0")
            dimensions = "x".join(sizes)
            
            print(f"{filename:<25} {points:<8,} Ø{diameter:<6.1f} {axis:<6} {time_min:<7.1f}m {distance:<9.0f}mm {dimensions:<15}")
    
    def find_optimal_strategies(self) -> None:
        """Trova le strategie ottimali per diversi obiettivi"""
        if not self.files_data:
            return
        
        print("\n🎯 STRATEGIE OTTIMALI")
        print("=" * 50)
        
        # Trova il più veloce
        fastest = min(self.files_data.items(), key=lambda x: x[1]['estimated_time'])
        print(f"⚡ Più veloce: {fastest[1]['filename']} ({fastest[1]['estimated_time']:.1f} min)")
        
        # Trova il più preciso (più punti)
        most_precise = max(self.files_data.items(), 
                          key=lambda x: x[1].get('tool_info', {}).get('total_points', 0))
        points = most_precise[1].get('tool_info', {}).get('total_points', 0)
        print(f"🎯 Più preciso: {most_precise[1]['filename']} ({points:,} punti)")
        
        # Trova il più bilanciato (rapporto tempo/precisione)
        balanced_scores = []
        for filepath, data in self.files_data.items():
            points = data.get('tool_info', {}).get('total_points', 1)
            time = data['estimated_time']
            if time > 0:
                score = points / time  # punti per minuto
                balanced_scores.append((filepath, data, score))
        
        if balanced_scores:
            best_balanced = max(balanced_scores, key=lambda x: x[2])
            print(f"⚖️ Più bilanciato: {best_balanced[1]['filename']} ({best_balanced[2]:.0f} punti/min)")

def main():
    """Funzione principale"""
    print("🔍 Analizzatore File G-code")
    print("=" * 50)
    
    analyzer = GCodeAnalyzer()
    analyzer.analyze_all_files("*.nc")
    
    if not analyzer.files_data:
        print("❌ Nessun file .nc trovato nella directory corrente")
        print("💡 Esegui prima main.py o examples.py per generare i file")
        return
    
    print(f"📁 Trovati {len(analyzer.files_data)} file G-code")
    
    # Stampa tabella comparativa
    analyzer.print_comparison_table()
    
    # Trova strategie ottimali
    analyzer.find_optimal_strategies()
    
    # Chiedi se mostrare dettagli di file specifici
    print(f"\n📋 File disponibili per analisi dettagliata:")
    files = list(analyzer.files_data.keys())
    for i, filepath in enumerate(files, 1):
        filename = analyzer.files_data[filepath]['filename']
        print(f"  {i}. {filename}")
    
    print(f"\n💡 Per analisi dettagliata di un file specifico:")
    print(f"   python analyze_gcode.py <nome_file.nc>")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        # Analizza file specifico
        filepath = sys.argv[1]
        analyzer = GCodeAnalyzer()
        data = analyzer.analyze_file(filepath)
        if data:
            analyzer.files_data[filepath] = data
            analyzer.print_file_summary(filepath)
        else:
            print(f"❌ Impossibile analizzare {filepath}")
    else:
        # Analizza tutti i file
        main()
