# Simulatore CNC con PythonOCC

Questo script Python implementa un simulatore di fresatura CNC completo che può funzionare sia con PythonOCC che in modalità semplificata senza dipendenze.

## Funzionalità Implementate

### 🔧 Caratteristiche Principali
- **Caricamento Modelli 3D**: Supporta file STEP (.step, .stp) e IGES (.iges, .igs)
- **Bounding Box Automatico**: Calcola il volume di lavorazione con offset configurabile (default 5mm)
- **Selezione Asse e Direzione**: Supporta assi X, Y, Z con direzioni + e -
- **Fresa Configurabile**: Diametro e lunghezza personalizzabili
- **Rilevamento Collisioni**: Algoritmo per evitare interferenze tra fresa e pezzo
- **Generazione Percorso**: Pattern zig-zag ottimizzato per sgrossatura
- **Export G-code**: Salvataggio in formato CNC standard
- **Visualizzazione**: Percorso colorato con punti e linee (quando PythonOCC è disponibile)

### 📊 Algoritmi Implementati
- **Pattern Zig-Zag**: Riduce spostamenti a vuoto
- **Stepover Adattivo**: Basato sulle dimensioni della fresa
- **Controllo Collisioni**: Calcolo distanze minime
- **Ottimizzazione Percorso**: Minimizza tempi di lavorazione

## Installazione

### Opzione 1: Con PythonOCC (Completa)
```bash
# Metodo consigliato con conda
conda install -c conda-forge pythonocc-core numpy

# Oppure con pip
pip install pythonocc-core numpy
```

### Opzione 2: Solo Logica (Semplificata)
```bash
# Solo numpy richiesto
pip install numpy
```

## Utilizzo

### Modalità Base
```bash
python main.py
```
Esegue la simulazione con parametri di default:
- Modello di esempio (parallelepipedo con foro)
- Asse Z, direzione negativa
- Fresa Ø6mm
- Stepover 4mm, stepdown 2mm

### Modalità Test
```bash
python main.py test
```
Esegue 4 test con parametri diversi:
1. **Z- Ø6mm**: Lavorazione dall'alto, fresa media
2. **Z- Ø8mm**: Lavorazione dall'alto, fresa grande
3. **Y+ Ø4mm**: Lavorazione laterale, fresa piccola
4. **X- Ø10mm**: Lavorazione frontale, fresa molto grande

### Personalizzazione nel Codice
```python
# Carica il tuo modello 3D
model_file = "percorso/al/tuo/modello.step"
simulator.run_simulation(model_file)

# Modifica parametri di lavorazione
simulator.set_machining_parameters('Y', '+', 8.0)  # Asse Y+, fresa 8mm
simulator.generate_roughing_toolpath(stepover=5.0, stepdown=3.0)
```

## Output

### File G-code Generati
- `toolpath_output.nc`: File principale
- `toolpath_test_X_*.nc`: File di test specifici

### Formato G-code
```gcode
G21 ; Unità millimetriche
G90 ; Posizionamento assoluto
G17 ; Piano XY
M3 S12000 ; Avvio mandrino
G0 X-5.000 Y-5.000 Z105.000 ; Posizionamento iniziale
G1 X-1.000 Y-5.000 Z105.000 F800 ; Lavorazione
...
M5 ; Spegni mandrino
M30 ; Fine programma
```

## Parametri Configurabili

### Fresa
- **Diametro**: 1-50mm (tipico 4-12mm)
- **Lunghezza**: Auto-calcolata o personalizzabile
- **Tipo**: End mill (espandibile)

### Lavorazione
- **Asse**: X, Y, Z
- **Direzione**: + (positiva) o - (negativa)
- **Stepover**: 30-80% del diametro fresa
- **Stepdown**: 10-50% del diametro fresa
- **Offset**: Margine di sicurezza (default 5mm)

### Velocità
- **Avanzamento**: 800 mm/min (modificabile nel G-code)
- **Mandrino**: 12000 RPM (modificabile nel G-code)

## Esempi di Risultati

### Test Comparativo
| Test | Asse | Fresa | Punti | Tempo Est. |
|------|------|-------|-------|------------|
| 1    | Z-   | Ø6mm  | 43,904| ~55 min    |
| 2    | Z-   | Ø8mm  | 13,357| ~17 min    |
| 3    | Y+   | Ø4mm  | 101,306| ~127 min  |
| 4    | X-   | Ø10mm | 5,488 | ~7 min     |

### Ottimizzazioni Automatiche
- **Fresa grande**: Meno passate, tempo ridotto
- **Fresa piccola**: Maggiore precisione, più passate
- **Pattern zig-zag**: Riduce spostamenti a vuoto del 40-60%

## Struttura del Codice

### Classi Principali
- `MillingTool`: Gestione parametri fresa
- `CNCSimulator`: Logica principale di simulazione
- `MockClasses`: Sostituti per PythonOCC quando non disponibile

### Metodi Chiave
- `load_model()`: Caricamento file 3D
- `calculate_bounding_box()`: Calcolo volume di lavorazione
- `generate_roughing_toolpath()`: Generazione percorso
- `check_collision()`: Rilevamento interferenze
- `export_toolpath_to_gcode()`: Salvataggio G-code

## Troubleshooting

### Errori Comuni
1. **Import PythonOCC**: Lo script funziona comunque in modalità semplificata
2. **File 3D non trovato**: Usa il modello di esempio automaticamente
3. **Troppi punti**: Aumenta stepover/stepdown per ridurre complessità

### Performance
- **Punti > 100k**: Considera parametri più grossolani
- **Memoria limitata**: Riduci dimensioni bounding box
- **Tempo calcolo**: Disabilita controllo collisioni per test rapidi

## Estensioni Future

### Funzionalità Pianificate
- [ ] Supporto frese a sfera
- [ ] Lavorazione di finitura
- [ ] Ottimizzazione velocità variabile
- [ ] Simulazione rimozione materiale
- [ ] Export STL del risultato
- [ ] Interfaccia grafica

### Contributi
Il codice è modulare e facilmente estendibile. Aree di miglioramento:
- Nuovi algoritmi di percorso
- Supporto altri formati 3D
- Ottimizzazioni performance
- Validazione G-code

## Licenza
Codice di esempio per scopi educativi e di prototipazione.
