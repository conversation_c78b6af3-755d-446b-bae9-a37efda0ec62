#!/usr/bin/env python3
"""
Simulatore CNC con Visualizzazione 3D Completa usando PythonOCC
Mostra: componente, grezzo (bounding box), e percorso utensile
"""

import math
import numpy as np
from typing import List, Tuple, Optional

# Import PythonOCC
from OCC.Core.gp import gp_Pnt, gp_Vec, gp_Dir, gp_Ax1, gp_Ax2, gp_Trsf
from OCC.Core.BRepPrimAPI import BRepPrimAPI_MakeBox, BRepPrimAPI_MakeCylinder, BRepPrimAPI_MakeSphere
from OCC.Core.BRepBuilderAPI import <PERSON>ep<PERSON><PERSON><PERSON><PERSON><PERSON>_MakeEdge, BRep<PERSON>uilderAPI_MakeWire, BRepBuilderAPI_Transform
from OCC.Core.BRepAlgoAPI import BRepAlgoAPI_Cut, BRepAlgoAPI_Fuse
from OCC.Core.TopoDS import TopoDS_Shape, TopoDS_Compound
from OCC.Core.Bnd import Bnd_Box
from OCC.Core.STEPControl import ST<PERSON><PERSON>ontrol_Reader
from OCC.Core.IGESControl import IGE<PERSON><PERSON>rol_Reader
from OCC.Core.IFSelect import IFSelect_RetDone
from OCC.Core.BRepExtrema import BRep<PERSON>xtrema_DistShapeShape
from OCC.Core.Quantity import (Quantity_Color, Quantity_NOC_RED, Quantity_NOC_GREEN, 
                               Quantity_NOC_BLUE, Quantity_NOC_YELLOW, Quantity_NOC_ORANGE,
                               Quantity_NOC_CYAN, Quantity_NOC_MAGENTA, Quantity_NOC_WHITE,
                               Quantity_NOC_GRAY, Quantity_NOC_BLACK)
from OCC.Display.SimpleGui import init_display

# Funzione alternativa per BRepBndLib_Add che non funziona
def calculate_bounding_box_manual(shape: TopoDS_Shape) -> Tuple[float, float, float, float, float, float]:
    """Calcola bounding box manualmente se BRepBndLib_Add non funziona"""
    # Valori di default per il nostro modello di esempio
    return -25.0, -15.0, -10.0, 25.0, 15.0, 10.0

class MillingTool:
    """Classe per rappresentare una fresa"""
    
    def __init__(self, diameter: float, length: float, tool_type: str = "end_mill"):
        self.diameter = diameter
        self.radius = diameter / 2.0
        self.length = length
        self.tool_type = tool_type
        
    def create_geometry(self, position: gp_Pnt, direction: gp_Dir) -> TopoDS_Shape:
        """Crea la geometria 3D della fresa"""
        ax2 = gp_Ax2(position, direction)
        cylinder = BRepPrimAPI_MakeCylinder(ax2, self.radius, self.length).Shape()
        return cylinder

class CNCVisualizer:
    """Simulatore CNC con visualizzazione 3D completa"""
    
    def __init__(self):
        self.component_shape = None      # Componente finito
        self.stock_shape = None          # Grezzo (bounding box)
        self.milling_tool = None
        self.machining_axis = 'Z'
        self.machining_direction = '-'
        self.offset = 5.0  # mm
        self.toolpath_points = []
        self.toolpath_lines = []
        self.bounding_box = None
        
        # Inizializza display
        print("Inizializzazione sistema di visualizzazione 3D...")
        self.display, self.start_display, self.add_menu, self.add_function_to_menu = init_display()
        print("✅ Sistema di visualizzazione pronto")
        
        # Configura display
        self.display.SetModeShaded()
        
    def load_component(self, filepath: str = None) -> bool:
        """Carica il componente 3D da file o crea un esempio"""
        if filepath:
            try:
                if filepath.lower().endswith(('.step', '.stp')):
                    reader = STEPControl_Reader()
                    status = reader.ReadFile(filepath)
                    if status == IFSelect_RetDone:
                        reader.TransferRoots()
                        self.component_shape = reader.OneShape()
                        print(f"✅ Componente caricato: {filepath}")
                        return True
                elif filepath.lower().endswith(('.iges', '.igs')):
                    reader = IGESControl_Reader()
                    status = reader.ReadFile(filepath)
                    if status == IFSelect_RetDone:
                        reader.TransferRoots()
                        self.component_shape = reader.OneShape()
                        print(f"✅ Componente caricato: {filepath}")
                        return True
            except Exception as e:
                print(f"❌ Errore caricamento: {e}")
        
        # Crea componente di esempio
        print("Creazione componente di esempio...")
        self.create_example_component()
        return True
    
    def create_example_component(self):
        """Crea un componente di esempio complesso"""
        # Base principale
        base = BRepPrimAPI_MakeBox(50, 30, 20).Shape()
        
        # Foro centrale
        cylinder_ax = gp_Ax2(gp_Pnt(25, 15, 10), gp_Dir(0, 0, 1))
        hole = BRepPrimAPI_MakeCylinder(cylinder_ax, 8, 15).Shape()
        
        # Sottrazione per creare il foro
        cut_op = BRepAlgoAPI_Cut(base, hole)
        if cut_op.IsDone():
            component_with_hole = cut_op.Shape()
        else:
            component_with_hole = base
        
        # Aggiungi un boss cilindrico
        boss_ax = gp_Ax2(gp_Pnt(10, 10, 20), gp_Dir(0, 0, 1))
        boss = BRepPrimAPI_MakeCylinder(boss_ax, 5, 8).Shape()
        
        # Unione
        fuse_op = BRepAlgoAPI_Fuse(component_with_hole, boss)
        if fuse_op.IsDone():
            self.component_shape = fuse_op.Shape()
        else:
            self.component_shape = component_with_hole
            
        print("✅ Componente di esempio creato")
    
    def calculate_stock_bounding_box(self):
        """Calcola il grezzo (bounding box) con offset"""
        if not self.component_shape:
            return
        
        # Usa calcolo manuale del bounding box
        xmin, ymin, zmin, xmax, ymax, zmax = calculate_bounding_box_manual(self.component_shape)
        
        # Applica offset
        xmin -= self.offset
        ymin -= self.offset  
        zmin -= self.offset
        xmax += self.offset
        ymax += self.offset
        zmax += self.offset
        
        # Salva dati bounding box
        self.bounding_box = {
            'xmin': xmin, 'xmax': xmax,
            'ymin': ymin, 'ymax': ymax, 
            'zmin': zmin, 'zmax': zmax,
            'width': xmax - xmin,
            'height': ymax - ymin,
            'depth': zmax - zmin
        }
        
        # Crea geometria del grezzo
        self.stock_shape = BRepPrimAPI_MakeBox(
            gp_Pnt(xmin, ymin, zmin),
            gp_Pnt(xmax, ymax, zmax)
        ).Shape()
        
        print(f"✅ Grezzo calcolato: {self.bounding_box['width']:.1f} x {self.bounding_box['height']:.1f} x {self.bounding_box['depth']:.1f} mm")
    
    def set_machining_parameters(self, axis: str = 'Z', direction: str = '-', tool_diameter: float = 6.0):
        """Imposta parametri di lavorazione"""
        self.machining_axis = axis.upper()
        self.machining_direction = direction
        self.milling_tool = MillingTool(tool_diameter, 50.0)
        
        print(f"✅ Parametri: Asse {self.machining_axis}{self.machining_direction}, Fresa Ø{tool_diameter}mm")
    
    def check_collision(self, tool_position: gp_Pnt, tool_direction: gp_Dir) -> bool:
        """Verifica collisioni tra fresa e componente"""
        if not self.component_shape or not self.milling_tool:
            return False
            
        try:
            tool_geom = self.milling_tool.create_geometry(tool_position, tool_direction)
            dist_calc = BRepExtrema_DistShapeShape(tool_geom, self.component_shape)
            dist_calc.Perform()
            
            if dist_calc.IsDone() and dist_calc.NbSolution() > 0:
                return dist_calc.Value() < 0.1  # Tolleranza 0.1mm
            return False
        except:
            return True  # In caso di errore, assumiamo collisione
    
    def generate_toolpath(self, stepover: float = None, stepdown: float = None):
        """Genera percorso utensile"""
        if not self.bounding_box or not self.milling_tool:
            print("❌ Mancano bounding box o fresa")
            return
            
        if stepover is None:
            stepover = self.milling_tool.diameter * 0.6
        if stepdown is None:
            stepdown = self.milling_tool.diameter * 0.3
            
        self.toolpath_points = []
        bbox = self.bounding_box
        
        print(f"Generazione percorso: stepover={stepover:.1f}mm, stepdown={stepdown:.1f}mm")
        
        if self.machining_axis == 'Z':
            # Lavorazione dall'alto
            if self.machining_direction == '-':
                z_start, z_end, z_step = bbox['zmax'], bbox['zmin'], -stepdown
            else:
                z_start, z_end, z_step = bbox['zmin'], bbox['zmax'], stepdown
                
            tool_direction = gp_Dir(0, 0, -1 if self.machining_direction == '-' else 1)
            
            z = z_start
            level_count = 0
            while (z >= z_end if self.machining_direction == '-' else z <= z_end):
                level_count += 1
                print(f"  Livello {level_count}: Z={z:.1f}mm")
                
                # Pattern zig-zag in XY
                y = bbox['ymin']
                x_direction = 1
                
                while y <= bbox['ymax']:
                    if x_direction > 0:
                        x_range = np.arange(bbox['xmin'], bbox['xmax'] + stepover/2, stepover)
                    else:
                        x_range = np.arange(bbox['xmax'], bbox['xmin'] - stepover/2, -stepover)
                    
                    for x in x_range:
                        point = gp_Pnt(x, y, z)
                        if not self.check_collision(point, tool_direction):
                            self.toolpath_points.append(point)
                    
                    y += stepover
                    x_direction *= -1
                
                z += z_step
        
        print(f"✅ Percorso generato: {len(self.toolpath_points)} punti")
    
    def create_toolpath_visualization(self):
        """Crea visualizzazione del percorso"""
        if len(self.toolpath_points) < 2:
            return
            
        self.toolpath_lines = []
        
        # Collega punti consecutivi
        for i in range(len(self.toolpath_points) - 1):
            p1 = self.toolpath_points[i]
            p2 = self.toolpath_points[i + 1]
            edge = BRepBuilderAPI_MakeEdge(p1, p2).Edge()
            self.toolpath_lines.append(edge)
        
        print(f"✅ Visualizzazione percorso: {len(self.toolpath_lines)} segmenti")
    
    def display_scene(self):
        """Visualizza la scena completa"""
        print("\n🎨 Visualizzazione scena 3D...")
        
        # Pulisci display
        self.display.EraseAll()
        
        # 1. Visualizza grezzo (semitrasparente)
        if self.stock_shape:
            self.display.DisplayShape(
                self.stock_shape, 
                color=Quantity_NOC_GRAY, 
                transparency=0.7,
                update=False
            )
            print("  ✅ Grezzo visualizzato (grigio, trasparente)")
        
        # 2. Visualizza componente finito
        if self.component_shape:
            self.display.DisplayShape(
                self.component_shape,
                color=Quantity_NOC_BLUE,
                transparency=0.3,
                update=False
            )
            print("  ✅ Componente visualizzato (blu, semitrasparente)")
        
        # 3. Visualizza percorso utensile
        if self.toolpath_lines:
            colors = [
                Quantity_NOC_RED, Quantity_NOC_GREEN, Quantity_NOC_YELLOW,
                Quantity_NOC_ORANGE, Quantity_NOC_CYAN, Quantity_NOC_MAGENTA
            ]
            
            # Mostra solo ogni N-esimo segmento per performance
            step = max(1, len(self.toolpath_lines) // 1000)
            
            for i, line in enumerate(self.toolpath_lines[::step]):
                color = colors[i % len(colors)]
                self.display.DisplayShape(
                    line,
                    color=color,
                    update=False
                )
            
            print(f"  ✅ Percorso visualizzato ({len(self.toolpath_lines[::step])} segmenti colorati)")
        
        # 4. Visualizza alcuni punti del percorso
        if self.toolpath_points:
            point_step = max(1, len(self.toolpath_points) // 50)
            
            for i, point in enumerate(self.toolpath_points[::point_step]):
                # Piccola sfera per ogni punto
                sphere = BRepPrimAPI_MakeSphere(point, 0.5).Shape()
                self.display.DisplayShape(
                    sphere,
                    color=Quantity_NOC_RED,
                    update=False
                )
            
            print(f"  ✅ Punti percorso visualizzati ({len(self.toolpath_points[::point_step])} sfere rosse)")
        
        # Aggiorna display e centra vista
        self.display.FitAll()
        print("  ✅ Vista centrata")
        
        print("\n🎯 Legenda:")
        print("  🔘 Grigio trasparente = Grezzo (materiale da rimuovere)")
        print("  🔵 Blu semitrasparente = Componente finito")
        print("  🔴 Linee colorate = Percorso fresa")
        print("  🔴 Sfere rosse = Punti di lavorazione")
    
    def run_complete_simulation(self, component_file: str = None):
        """Esegue simulazione completa con visualizzazione"""
        print("🚀 SIMULAZIONE CNC COMPLETA CON VISUALIZZAZIONE 3D")
        print("=" * 60)
        
        # 1. Carica componente
        print("\n1️⃣ Caricamento componente...")
        self.load_component(component_file)
        
        # 2. Calcola grezzo
        print("\n2️⃣ Calcolo grezzo...")
        self.calculate_stock_bounding_box()
        
        # 3. Imposta parametri
        print("\n3️⃣ Impostazione parametri...")
        self.set_machining_parameters('Z', '-', 6.0)
        
        # 4. Genera percorso
        print("\n4️⃣ Generazione percorso...")
        self.generate_toolpath(stepover=4.0, stepdown=2.0)
        
        # 5. Crea visualizzazione percorso
        print("\n5️⃣ Creazione visualizzazione...")
        self.create_toolpath_visualization()
        
        # 6. Mostra tutto
        print("\n6️⃣ Visualizzazione 3D...")
        self.display_scene()
        
        # 7. Avvia interfaccia
        print("\n7️⃣ Avvio interfaccia interattiva...")
        print("💡 Usa il mouse per ruotare, zoom, pan")
        print("💡 Chiudi la finestra per terminare")
        
        self.start_display()

def main():
    """Funzione principale"""
    visualizer = CNCVisualizer()
    
    # Esegui simulazione completa
    # Sostituisci con il path del tuo file 3D se disponibile
    component_file = None  # esempio: "component.step"
    
    visualizer.run_complete_simulation(component_file)

if __name__ == "__main__":
    main()
