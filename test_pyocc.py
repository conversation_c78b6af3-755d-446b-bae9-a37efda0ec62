from OCC.Core.Bnd import Bnd_Box
from OCC.Core.BRepBndLib import Add as brepbnd_Add
from OCC.Core.gp import gp_Pnt
from OCC.Core.BRepPrimAPI import BRepPrimAPI_MakeSphere, BRepPrimAPI_MakeBox
from OCC.Display.SimpleGui import init_display


def create_bounding_box(shape, offset):
    """
    Calcola il bounding box di `shape` e lo estende di `offset` lungo tutte le direzioni.
    Restituisce una forma OCC che rappresenta la scatola.
    """
    bbox = Bnd_Box()
    # Aggiunge la shape al bounding box
    brepbnd_Add(shape, bbox)
    # Estrai le coordinate min e max
    xmin, ymin, zmin, xmax, ymax, zmax = bbox.Get()

    # Applica l'offset
    xmin -= offset
    ymin -= offset
    zmin -= offset
    xmax += offset
    ymax += offset
    zmax += offset

    # Crea un box con i due vertici opposti
    box = BRepPrimAPI_MakeBox(
        gp_Pnt(xmin, ymin, zmin),
        gp_Pnt(xmax, ymax, zmax)
    ).Shape()

    return box


def main():
    # Inizializza il display
    display, start_display, add_menu, add_function_to_menu = init_display()

    # Crea un modello di esempio: una sfera di raggio 50
    sphere = BRepPrimAPI_MakeSphere(50).Shape()

    # Offset desiderato
    offset = 10
    # Calcola il bounding box esteso
    bbox = create_bounding_box(sphere, offset)

    # Visualizza la sfera
    display.DisplayShape(sphere, update=False)
    # Visualizza il bounding box in rosso e semitrasparente (transparency=0.7)
    display.DisplayShape(bbox, color=(1.0, 0.0, 0.0), transparency=0.7, update=True)

    # Avvia il loop di visualizzazione
    start_display()


if __name__ == "__main__":
    main()
