#!/usr/bin/env python3
"""
Script per simulazione di fresatura CNC con PythonOCC
Funzionalità:
- Caricamento modello 3D
- Creazione bounding box con offset
- Selezione asse e direzione di lavorazione
- Calcolo percorso fresa con rilevamento collisioni
- Visualizzazione percorso colorato
"""

import math
import numpy as np
from typing import List, Tuple, Optional

# Versione semplificata senza PythonOCC per testare la logica
print("=== Simulatore CNC Semplificato ===")
print("Questa versione implementa la logica senza dipendenze PythonOCC")

# Classi mock per sostituire PythonOCC
class MockPoint:
    def __init__(self, x, y, z):
        self._x, self._y, self._z = x, y, z
    def X(self): return self._x
    def Y(self): return self._y
    def Z(self): return self._z
    def __str__(self): return f"Point({self._x:.2f}, {self._y:.2f}, {self._z:.2f})"

class MockDirection:
    def __init__(self, x, y, z):
        self._x, self._y, self._z = x, y, z

class MockVector:
    def __init__(self, p1, p2):
        self.p1, self.p2 = p1, p2

class MockAxis2:
    def __init__(self, point, direction):
        self.point, self.direction = point, direction

class MockTransform:
    def __init__(self):
        pass
    def SetTranslation(self, vec):
        pass

class MockShape:
    def __init__(self, name="MockShape"):
        self.name = name
    def Shape(self):
        return self

class MockBox:
    def __init__(self):
        self.xmin = self.ymin = self.zmin = 0
        self.xmax = self.ymax = self.zmax = 100
    def Get(self):
        return self.xmin, self.ymin, self.zmin, self.xmax, self.ymax, self.zmax

class MockBuilder:
    def __init__(self, *args):
        pass
    def Shape(self):
        return MockShape()
    def Edge(self):
        return MockShape()
    def IsDone(self):
        return True

class MockReader:
    def ReadFile(self, filepath):
        return "IFSelect_RetDone"
    def TransferRoots(self):
        pass
    def OneShape(self):
        return MockShape()

class MockDistanceCalculator:
    def __init__(self, shape1, shape2):
        pass
    def Perform(self):
        pass
    def IsDone(self):
        return True
    def NbSolution(self):
        return 1
    def Value(self):
        return 5.0  # Distanza mock

class MockCutOperation:
    def __init__(self, shape1, shape2):
        self.result = MockShape()
    def IsDone(self):
        return True
    def Shape(self):
        return self.result

# Alias per compatibilità
gp_Pnt = MockPoint
gp_Dir = MockDirection
gp_Vec = MockVector
gp_Ax2 = MockAxis2
gp_Trsf = MockTransform
TopoDS_Shape = MockShape
Bnd_Box = MockBox
BRepPrimAPI_MakeBox = MockBuilder
BRepPrimAPI_MakeCylinder = MockBuilder
BRepBuilderAPI_MakeEdge = MockBuilder
BRepBuilderAPI_Transform = MockBuilder
BRepExtrema_DistShapeShape = MockDistanceCalculator
BRepAlgoAPI_Cut = MockCutOperation
STEPControl_Reader = MockReader
IGESControl_Reader = MockReader
IFSelect_RetDone = "IFSelect_RetDone"

def BRepBndLib_Add(shape, bbox):
    """Mock function per calcolo bounding box"""
    pass

PYTHONOCC_AVAILABLE = False

DISPLAY_AVAILABLE = False
print("Modalità solo calcolo - nessuna visualizzazione 3D")

def init_display():
    return None, None, None, None

class MockDisplay:
    def EraseAll(self): pass
    def DisplayShape(self, shape, color=None, transparency=None, linewidth=None): pass
    def FitAll(self): pass


class MillingTool:
    """Classe per rappresentare una fresa"""

    def __init__(self, diameter: float, length: float, tool_type: str = "end_mill"):
        self.diameter = diameter
        self.radius = diameter / 2.0
        self.length = length
        self.tool_type = tool_type

    def create_geometry(self, position: gp_Pnt, direction: gp_Dir) -> TopoDS_Shape:
        """Crea la geometria 3D della fresa"""
        # Crea un cilindro per rappresentare la fresa
        ax2 = gp_Ax2(position, direction)
        cylinder = BRepPrimAPI_MakeCylinder(ax2, self.radius, self.length).Shape()
        return cylinder


class CNCSimulator:
    """Simulatore principale per fresatura CNC"""

    def __init__(self):
        self.model_shape = None
        self.bounding_box = None
        self.bbox_shape = None
        self.milling_tool = None
        self.machining_axis = 'Z'
        self.machining_direction = '-'  # '+' o '-'
        self.offset = 5.0  # mm
        self.toolpath_points = []
        self.toolpath_lines = []

        # Inizializza display mock
        self.display = MockDisplay()
        self.start_display = None

    def load_model(self, filepath: str) -> bool:
        """Carica un modello 3D da file"""
        try:
            if filepath.lower().endswith('.step') or filepath.lower().endswith('.stp'):
                reader = STEPControl_Reader()
                status = reader.ReadFile(filepath)
                if status == IFSelect_RetDone:
                    reader.TransferRoots()
                    self.model_shape = reader.OneShape()
            elif filepath.lower().endswith('.iges') or filepath.lower().endswith('.igs'):
                reader = IGESControl_Reader()
                status = reader.ReadFile(filepath)
                if status == IFSelect_RetDone:
                    reader.TransferRoots()
                    self.model_shape = reader.OneShape()
            else:
                print(f"Formato file non supportato: {filepath}")
                return False

            print(f"Modello caricato: {filepath}")
            return True

        except Exception as e:
            print(f"Errore nel caricamento del modello: {e}")
            return False

    def create_sample_model(self):
        """Crea un modello di esempio se non è disponibile un file"""
        # Crea un parallelepipedo di esempio
        box = BRepPrimAPI_MakeBox(50, 30, 20).Shape()

        # Crea un cilindro da sottrarre
        cylinder_ax = gp_Ax2(gp_Pnt(25, 15, 10), gp_Dir(0, 0, 1))
        cylinder = BRepPrimAPI_MakeCylinder(cylinder_ax, 8, 15).Shape()

        # Sottrazione booleana
        cut_op = BRepAlgoAPI_Cut(box, cylinder)
        if cut_op.IsDone():
            self.model_shape = cut_op.Shape()
        else:
            self.model_shape = box

        print("Modello di esempio creato")

    def calculate_bounding_box(self):
        """Calcola il bounding box del modello con offset"""
        if not self.model_shape:
            return

        bbox = Bnd_Box()
        BRepBndLib_Add(self.model_shape, bbox)

        # Ottieni coordinate min/max
        xmin, ymin, zmin, xmax, ymax, zmax = bbox.Get()

        # Applica offset
        xmin -= self.offset
        ymin -= self.offset
        zmin -= self.offset
        xmax += self.offset
        ymax += self.offset
        zmax += self.offset

        # Salva bounding box
        self.bounding_box = {
            'xmin': xmin, 'xmax': xmax,
            'ymin': ymin, 'ymax': ymax,
            'zmin': zmin, 'zmax': zmax,
            'width': xmax - xmin,
            'height': ymax - ymin,
            'depth': zmax - zmin
        }

        # Crea geometria del bounding box
        self.bbox_shape = BRepPrimAPI_MakeBox(
            gp_Pnt(xmin, ymin, zmin),
            gp_Pnt(xmax, ymax, zmax)
        ).Shape()

        print(f"Bounding box calcolato: {self.bounding_box['width']:.1f} x {self.bounding_box['height']:.1f} x {self.bounding_box['depth']:.1f} mm")

    def set_machining_parameters(self, axis: str = 'Z', direction: str = '-', tool_diameter: float = 6.0):
        """Imposta i parametri di lavorazione"""
        self.machining_axis = axis.upper()
        self.machining_direction = direction
        self.milling_tool = MillingTool(tool_diameter, 50.0)

        print(f"Parametri lavorazione impostati: Asse {self.machining_axis}{self.machining_direction}, Fresa Ø{tool_diameter}mm")

    def check_collision(self, tool_position: gp_Pnt, tool_direction: gp_Dir) -> bool:
        """Verifica se la fresa in una data posizione collidera con il modello"""
        if not self.model_shape or not self.milling_tool:
            return False

        try:
            # Crea geometria fresa nella posizione specificata
            tool_geom = self.milling_tool.create_geometry(tool_position, tool_direction)

            # Calcola distanza minima tra fresa e pezzo
            dist_calc = BRepExtrema_DistShapeShape(tool_geom, self.model_shape)
            dist_calc.Perform()

            if dist_calc.IsDone() and dist_calc.NbSolution() > 0:
                min_distance = dist_calc.Value()
                # Se la distanza è troppo piccola, c'è collisione
                return min_distance < 0.1  # Tolleranza 0.1mm

            return False

        except Exception as e:
            print(f"Errore nel controllo collisioni: {e}")
            return True  # In caso di errore, assumiamo collisione

    def generate_roughing_toolpath(self, stepover: float = None, stepdown: float = None):
        """Genera il percorso per la sgrossatura"""
        if not self.bounding_box or not self.milling_tool:
            print("Errore: mancano bounding box o fresa")
            return

        # Parametri di default basati sulla fresa
        if stepover is None:
            stepover = self.milling_tool.diameter * 0.6  # 60% del diametro
        if stepdown is None:
            stepdown = self.milling_tool.diameter * 0.3  # 30% del diametro

        self.toolpath_points = []

        bbox = self.bounding_box

        # Determina parametri in base all'asse di lavorazione
        if self.machining_axis == 'Z':
            # Lavorazione dall'alto verso il basso o viceversa
            if self.machining_direction == '-':
                z_start = bbox['zmax']
                z_end = bbox['zmin']
                z_step = -stepdown
            else:
                z_start = bbox['zmin']
                z_end = bbox['zmax']
                z_step = stepdown

            tool_direction = gp_Dir(0, 0, -1 if self.machining_direction == '-' else 1)

            # Genera livelli Z
            z_levels = []
            z = z_start
            while (z >= z_end if self.machining_direction == '-' else z <= z_end):
                z_levels.append(z)
                z += z_step

            # Per ogni livello Z, genera percorso XY
            for i, z_level in enumerate(z_levels):
                level_points = []

                # Pattern a zig-zag in XY
                y = bbox['ymin']
                x_direction = 1

                while y <= bbox['ymax']:
                    if x_direction > 0:
                        x_range = np.arange(bbox['xmin'], bbox['xmax'] + stepover/2, stepover)
                    else:
                        x_range = np.arange(bbox['xmax'], bbox['xmin'] - stepover/2, -stepover)

                    for x in x_range:
                        point = gp_Pnt(x, y, z_level)

                        # Verifica collisioni
                        if not self.check_collision(point, tool_direction):
                            level_points.append(point)

                    y += stepover
                    x_direction *= -1  # Cambia direzione per zig-zag

                if level_points:
                    self.toolpath_points.extend(level_points)

        elif self.machining_axis == 'Y':
            # Lavorazione lungo Y
            if self.machining_direction == '-':
                y_start = bbox['ymax']
                y_end = bbox['ymin']
                y_step = -stepdown
            else:
                y_start = bbox['ymin']
                y_end = bbox['ymax']
                y_step = stepdown

            tool_direction = gp_Dir(0, -1 if self.machining_direction == '-' else 1, 0)

            # Simile implementazione per asse Y...
            # (implementazione semplificata)
            y = y_start
            while (y >= y_end if self.machining_direction == '-' else y <= y_end):
                for x in np.arange(bbox['xmin'], bbox['xmax'], stepover):
                    for z in np.arange(bbox['zmin'], bbox['zmax'], stepover):
                        point = gp_Pnt(x, y, z)
                        if not self.check_collision(point, tool_direction):
                            self.toolpath_points.append(point)
                y += y_step

        elif self.machining_axis == 'X':
            # Lavorazione lungo X
            if self.machining_direction == '-':
                x_start = bbox['xmax']
                x_end = bbox['xmin']
                x_step = -stepdown
            else:
                x_start = bbox['xmin']
                x_end = bbox['xmax']
                x_step = stepdown

            tool_direction = gp_Dir(-1 if self.machining_direction == '-' else 1, 0, 0)

            # Simile implementazione per asse X...
            x = x_start
            while (x >= x_end if self.machining_direction == '-' else x <= x_end):
                for y in np.arange(bbox['ymin'], bbox['ymax'], stepover):
                    for z in np.arange(bbox['zmin'], bbox['zmax'], stepover):
                        point = gp_Pnt(x, y, z)
                        if not self.check_collision(point, tool_direction):
                            self.toolpath_points.append(point)
                x += x_step

        print(f"Percorso generato: {len(self.toolpath_points)} punti")

    def create_toolpath_visualization(self):
        """Crea linee colorate per visualizzare il percorso"""
        if len(self.toolpath_points) < 2:
            return

        self.toolpath_lines = []

        # Collega punti consecutivi con linee
        for i in range(len(self.toolpath_points) - 1):
            p1 = self.toolpath_points[i]
            p2 = self.toolpath_points[i + 1]

            # Crea linea
            edge = BRepBuilderAPI_MakeEdge(p1, p2).Edge()
            self.toolpath_lines.append(edge)

    def visualize_scene(self):
        """Visualizza la scena completa (modalità mock)"""
        print("=== Visualizzazione Scena ===")

        # Visualizza modello originale
        if self.model_shape:
            print(f"- Modello 3D: {self.model_shape.name}")
            self.display.DisplayShape(self.model_shape, color='blue', transparency=0.3)

        # Visualizza bounding box
        if self.bbox_shape:
            print(f"- Bounding Box: {self.bounding_box['width']:.1f} x {self.bounding_box['height']:.1f} x {self.bounding_box['depth']:.1f} mm")
            self.display.DisplayShape(self.bbox_shape, color='gray', transparency=0.7)

        # Visualizza percorso utensile
        print(f"- Percorso utensile: {len(self.toolpath_lines)} segmenti")
        colors = ['red', 'green', 'yellow', 'orange', 'cyan', 'magenta']

        for i, line in enumerate(self.toolpath_lines):
            color = colors[i % len(colors)]
            self.display.DisplayShape(line, color=color, linewidth=2)

        # Visualizza alcuni punti del percorso
        sample_points = self.toolpath_points[::max(1, len(self.toolpath_points)//10)]
        print(f"- Punti campione visualizzati: {len(sample_points)}")

        for i, point in enumerate(sample_points):
            # Crea piccola sfera per ogni punto
            sphere = BRepPrimAPI_MakeCylinder(0.5, 1.0).Shape()

            # Trasla la sfera alla posizione del punto
            trsf = gp_Trsf()
            trsf.SetTranslation(gp_Vec(gp_Pnt(0,0,0), point))
            transformed_sphere = BRepBuilderAPI_Transform(sphere, trsf).Shape()

            self.display.DisplayShape(transformed_sphere, color='red')

        # Fit view
        self.display.FitAll()
        print("Visualizzazione completata (modalità mock)")

        # Stampa alcuni punti del percorso per debug
        if self.toolpath_points:
            print("\n=== Primi 10 punti del percorso ===")
            for i, point in enumerate(self.toolpath_points[:10]):
                print(f"Punto {i+1}: {point}")

            if len(self.toolpath_points) > 10:
                print(f"... e altri {len(self.toolpath_points)-10} punti")

    def run_simulation(self, model_file: str = None):
        """Esegue la simulazione completa"""
        print("=== Simulazione Fresatura CNC ===")

        # 1. Carica o crea modello
        if model_file and self.load_model(model_file):
            pass
        else:
            print("Creazione modello di esempio...")
            self.create_sample_model()

        # 2. Calcola bounding box
        print("Calcolo bounding box...")
        self.calculate_bounding_box()

        # 3. Imposta parametri lavorazione
        print("Impostazione parametri...")
        self.set_machining_parameters('Z', '-', 6.0)  # Asse Z, direzione negativa, fresa 6mm

        # 4. Genera percorso
        print("Generazione percorso...")
        self.generate_roughing_toolpath(stepover=4.0, stepdown=2.0)

        # 5. Crea visualizzazione percorso
        print("Creazione visualizzazione...")
        self.create_toolpath_visualization()

        # 6. Visualizza tutto
        print("Visualizzazione scena...")
        self.visualize_scene()

        # 7. Avvia interfaccia
        if self.start_display:
            print("Avvio interfaccia grafica...")
            self.start_display()
        else:
            print("Simulazione completata (modalità solo calcolo)")

            # Salva risultati in formato testuale
            self.export_toolpath_to_gcode("toolpath_output.nc")

    def export_toolpath_to_gcode(self, filename: str):
        """Esporta il percorso in formato G-code"""
        if not self.toolpath_points:
            print("Nessun percorso da esportare")
            return

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("; G-code generato dal simulatore CNC\n")
                f.write(f"; Fresa: D{self.milling_tool.diameter}mm\n")
                f.write(f"; Asse lavorazione: {self.machining_axis}{self.machining_direction}\n")
                f.write(f"; Punti totali: {len(self.toolpath_points)}\n")
                f.write("\n")
                f.write("G21 ; Unità millimetriche\n")
                f.write("G90 ; Posizionamento assoluto\n")
                f.write("G17 ; Piano XY\n")
                f.write("M3 S12000 ; Avvio mandrino\n")
                f.write("\n")

                # Primo punto - movimento rapido
                if self.toolpath_points:
                    p = self.toolpath_points[0]
                    f.write(f"G0 X{p.X():.3f} Y{p.Y():.3f} Z{p.Z():.3f} ; Posizionamento iniziale\n")

                # Percorso di lavorazione
                for i, point in enumerate(self.toolpath_points[1:], 1):
                    f.write(f"G1 X{point.X():.3f} Y{point.Y():.3f} Z{point.Z():.3f} F800 ; Punto {i}\n")

                f.write("\n")
                f.write("M5 ; Spegni mandrino\n")
                f.write("G0 Z50 ; Allontanamento sicurezza\n")
                f.write("M30 ; Fine programma\n")

            print(f"G-code salvato in: {filename}")

        except Exception as e:
            print(f"Errore nel salvataggio G-code: {e}")


def main():
    """Funzione principale"""
    import sys

    print("=== Simulatore CNC con PythonOCC ===")
    print("Opzioni disponibili:")
    print("1. Modalità semplificata (senza PythonOCC)")
    print("2. Prova con PythonOCC reale")
    print("3. Test con diversi parametri")

    # Parametri di esempio
    test_configs = [
        {'axis': 'Z', 'direction': '-', 'tool_diameter': 6.0, 'stepover': 4.0, 'stepdown': 2.0},
        {'axis': 'Z', 'direction': '-', 'tool_diameter': 8.0, 'stepover': 6.0, 'stepdown': 3.0},
        {'axis': 'Y', 'direction': '+', 'tool_diameter': 4.0, 'stepover': 3.0, 'stepdown': 1.5},
        {'axis': 'X', 'direction': '-', 'tool_diameter': 10.0, 'stepover': 8.0, 'stepdown': 4.0},
    ]

    if len(sys.argv) > 1 and sys.argv[1] == "test":
        # Modalità test con diversi parametri
        for i, config in enumerate(test_configs):
            print(f"\n=== Test {i+1}: {config} ===")
            simulator = CNCSimulator()
            simulator.create_sample_model()
            simulator.calculate_bounding_box()
            simulator.set_machining_parameters(config['axis'], config['direction'], config['tool_diameter'])
            simulator.generate_roughing_toolpath(config['stepover'], config['stepdown'])
            simulator.create_toolpath_visualization()

            # Salva G-code specifico per ogni test
            filename = f"toolpath_test_{i+1}_{config['axis']}{config['direction']}_D{config['tool_diameter']}.nc"
            simulator.export_toolpath_to_gcode(filename)
            print(f"Test {i+1} completato - {len(simulator.toolpath_points)} punti generati")
    else:
        # Modalità normale
        simulator = CNCSimulator()

        # Esegui simulazione
        # Sostituisci con il path del tuo file 3D se disponibile
        model_file = None  # esempio: "model.step"

        simulator.run_simulation(model_file)


if __name__ == "__main__":
    main()